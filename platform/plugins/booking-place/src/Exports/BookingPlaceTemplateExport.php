<?php

namespace Botble\BookingPlace\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class BookingPlaceTemplateExport implements FromArray, WithHeadings
{
    protected array $data;

    public function __construct(array $data = [])
    {
        if (empty($data)) {
            $this->data = $this->getDefaultTemplateData();
        } else {
            $this->data = $data;
        }
    }

    public function array(): array
    {
        return $this->data;
    }

    public function headings(): array
    {
        return [
            'name',
            'slug',
            'description',
            'status',
            'thumbnail',
            'reservation_start_time',
            'reservation_end_time',
            'address',
            'address_2',
            'city',
            'state',
            'postcode',
            'country',
            'coordinates',
            'phone_numbers',
            'emails',
            'website',
            'social_links',
            'iframe',
            'categories',
            'services'
        ];
    }

    protected function getDefaultTemplateData(): array
    {
        return [
            [
                'name' => 'Sample Restaurant',
                'slug' => 'sample-restaurant',
                'description' => 'A sample restaurant description with details about the place.',
                'status' => 'published',
                'thumbnail' => 'https://example.com/thumbnail.jpg',
                'reservation_start_time' => '09:00',
                'reservation_end_time' => '22:00',
                'address' => '123 Main Street',
                'address_2' => 'Suite 100',
                'city' => 'New York',
                'state' => 'NY',
                'postcode' => '10001',
                'country' => 'United States',
                'coordinates' => '40.7128,-74.0060',
                'phone_numbers' => '["******-0123", "******-0124"]',
                'emails' => '["<EMAIL>", "<EMAIL>"]',
                'website' => 'https://restaurant.com',
                'social_links' => '[{"platform": "facebook", "url": "https://facebook.com/restaurant"}, {"platform": "instagram", "url": "https://instagram.com/restaurant"}]',
                'iframe' => '<iframe src="https://maps.google.com/..."></iframe>',
                'categories' => 'Italian, Fine Dining',
                'services' => 'WiFi, Parking, Takeout'
            ],
            [
                'name' => 'Sample Hotel',
                'slug' => 'sample-hotel',
                'description' => 'A luxurious hotel with modern amenities.',
                'status' => 'published',
                'thumbnail' => 'https://example.com/hotel-thumbnail.jpg',
                'reservation_start_time' => '00:00',
                'reservation_end_time' => '23:59',
                'address' => '456 Hotel Avenue',
                'address_2' => '',
                'city' => 'Los Angeles',
                'state' => 'CA',
                'postcode' => '90210',
                'country' => 'United States',
                'coordinates' => '34.0522,-118.2437',
                'phone_numbers' => '["******-0567"]',
                'emails' => '["<EMAIL>"]',
                'website' => 'https://hotel.com',
                'social_links' => '[{"platform": "twitter", "url": "https://twitter.com/hotel"}]',
                'iframe' => '',
                'categories' => 'Luxury, Business',
                'services' => 'Spa, Gym, Room Service'
            ]
        ];
    }
}