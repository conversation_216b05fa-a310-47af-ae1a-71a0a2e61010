<?php

namespace Bo<PERSON>ble\BookingPlace\Http\Controllers;

use Bo<PERSON>ble\Base\Http\Actions\DeleteResourceAction;
use <PERSON><PERSON><PERSON>\BookingPlace\Http\Requests\BookingPlaceRequest;
use <PERSON><PERSON><PERSON>\BookingPlace\Http\Requests\BookingPlaceImportRequest;
use Bo<PERSON>ble\BookingPlace\Models\BookingPlace;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\BookingPlace\Tables\BookingPlaceTable;
use Bo<PERSON>ble\BookingPlace\Forms\BookingPlaceForm;
use Bo<PERSON>ble\BookingPlace\Imports\BookingPlaceImport;
use Bo<PERSON>ble\BookingPlace\Exports\BookingPlaceTemplateExport;
use Maatwebsite\Excel\Facades\Excel;

class BookingPlaceController extends BaseController
{
    public function __construct()
    {
        $this
            ->breadcrumb()
            ->add(trans('plugins/booking-place::booking-place.name'), route('booking-place.index'));
    }

    public function index(BookingPlaceTable $table)
    {
        $this->pageTitle(trans('plugins/booking-place::booking-place.name'));

        return $table->renderTable();
    }

    public function create()
    {
        $this->pageTitle(trans('plugins/booking-place::booking-place.create'));

        return BookingPlaceForm::create()->renderForm();
    }

    public function store(BookingPlaceRequest $request)
    {
        $form = BookingPlaceForm::create()->setRequest($request)->save();

        $bookingPlace = $form->getModel();

        // Sync categories only if bookingPlace exists and has id
        if ($bookingPlace && $bookingPlace->id && $request->has('category_ids')) {
            $bookingPlace->categories()->sync($request->input('category_ids', []));
        }
        if ($bookingPlace && $bookingPlace->id && $request->has('service_ids')) {
            $bookingPlace->services()->sync($request->input('service_ids', []));
        }

        return $this
            ->httpResponse()
            ->setPreviousUrl(route('booking-place.index'))
            ->setNextUrl(route('booking-place.edit', $bookingPlace->getKey()))
            ->setMessage(trans('core/base::notices.create_success_message'));
    }

    public function edit(BookingPlace $bookingPlace)
    {
        $this->pageTitle(trans('core/base::forms.edit_item', ['name' => $bookingPlace->name]));

        return BookingPlaceForm::createFromModel($bookingPlace)->renderForm();
    }

    public function update(BookingPlace $bookingPlace, BookingPlaceRequest $request)
    {
        if ($request->has('category_ids')) {
            $bookingPlace->categories()->sync($request->input('category_ids', []));
        }
        if ($request->has('service_ids')) {
            $bookingPlace->services()->sync($request->input('service_ids', []));
        }

        // Temporarily test direct status update
        if ($request->has('status')) {
            $bookingPlace->status = $request->input('status');
            $bookingPlace->save();
        }
        BookingPlaceForm::createFromModel($bookingPlace)
            ->setRequest($request)
            ->save();

        return $this
            ->httpResponse()
            ->setPreviousUrl(route('booking-place.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    public function destroy(BookingPlace $bookingPlace)
    {
        // Detach related categories and services to avoid foreign key constraint errors
        $bookingPlace->categories()->detach();
        $bookingPlace->services()->detach();
        return DeleteResourceAction::make($bookingPlace);
    }

    /**
     * Show the import form
     */
    public function import()
    {
        $this->pageTitle(trans('plugins/booking-place::import.title'));

        return view('plugins/booking-place::import.index');
    }

    /**
     * Process the import file
     */
    public function processImport(BookingPlaceImportRequest $request)
    {
        try {
            $import = new BookingPlaceImport();
            Excel::import($import, $request->file('file'));

            foreach ($import->failures() as $failure) {
                foreach ($failure->errors() as $error) {
                    $import->addError($failure->row(), $error);
                }
            }

            $successCount = $import->getSuccessCount();
            $failureCount = $import->getFailureCount();
            $errors = $import->getErrors();

            if ($successCount > 0) {
                $message = trans('plugins/booking-place::import.success', [
                    'success' => $successCount,
                    'total' => $successCount + $failureCount
                ]);
            } else {
                $message = trans('plugins/booking-place::import.no_records_imported');
            }

            return $this->httpResponse()
                ->setData([
                    'success_count' => $successCount,
                    'failure_count' => $failureCount,
                    'errors' => $errors,
                    'processed_rows' => $import->getProcessedRows()
                ])
                ->setMessage($message)
                ->setNextUrl(route('booking-place.index'));

        } catch (\Exception $e) {
            return $this->httpResponse()
                ->setError()
                ->setMessage(trans('plugins/booking-place::import.import_failed', [
                    'error' => $e->getMessage()
                ]));
        }
    }

    /**
     * Download the import template
     */
    public function downloadTemplate()
    {
        return Excel::download(new BookingPlaceTemplateExport(), 'booking_place_import_template_' . date('Y-m-d_H-i-s') . '.xlsx');
    }
}
